rootProject.name = 'connector'
include 'commons:azure-commons'
include 'commons:utility-commons'
include 'commons:insights-library'
include 'commons:blob-commons'
include 'commons:kafka-commons'
include 'commons:vault-client'
include 'commons:flow-commons'
include 'commons:inventory-commons'
include 'services:common-security-log'
include 'services:scaletest-eventhubs'
include 'services:load-logs-ingestion-api'
include 'services:law-sink'
include 'services:law-sampler'
include 'services:flow-enrichment'
include 'services:inventory-sink'
include 'services:log-flow-ingestion'
include 'services:scaletest-flow-ingestion'
include 'services:cef-log-connector'
include 'services:flow-puller'
include 'tools:flow-log-eventgrid:flow-log-eventgrid-failure-notifier'
include 'services:ip-classification-mmdb'
include 'services:label-recommendation'
include 'services:flow-push'
include 'services:insights-query-service'
include 'services:insights-search-service'
include 'services:policy-notification'
include 'services:flow-agg'
include 'services:flow-decorator'
include 'commons:cloudsecure-api'
include 'services:insights-onboarding-service'
include 'services:gateway-connector'
include 'services:network-device-sync'
include 'services:inventory-persistence'
include 'services:database-manager'
include 'services:integration-test-suite'
