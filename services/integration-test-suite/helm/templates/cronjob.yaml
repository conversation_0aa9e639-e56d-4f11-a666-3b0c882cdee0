apiVersion: batch/v1
kind: CronJob
metadata:
  name: cronjob
spec:
  schedule: {{ .Values.cronJob.schedule | quote }}
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: integration-test
              image: "{{ .Values.image.repositoryBase }}{{ .Values.image.repositoryName }}:{{ .Values.image.tag }}"
              imagePullPolicy: {{ .Values.image.pullPolicy }}
              command: ["java", "-cp", "build/classes/java/main:build/resources/main:build/libs/*", "com.illumio.integration.TestSuiteRunner"]
              env:
                - name: baseUrl
                  value: {{ .Values.insightsConfig.baseUrl | quote }}
                - name: tenantId
                  value: {{ .Values.insightsConfig.tenantId | quote }}
                - name: hourlyDiff
                  value: {{ .Values.insightsConfig.hourlyDiff | quote }}
                - name: JAVA_OPTS
                  value: "-Xmx512m"
          restartPolicy: Never