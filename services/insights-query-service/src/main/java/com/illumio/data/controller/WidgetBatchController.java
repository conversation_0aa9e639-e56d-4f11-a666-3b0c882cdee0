package com.illumio.data.controller;

import com.illumio.data.configuration.InsightsServiceConfiguration;
import com.illumio.data.model.WidgetBatchRequest;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.model.constants.RequestMetadata;
import com.illumio.data.response.WidgetBatchResponse;
import com.illumio.data.service.JWTService;
import com.illumio.data.service.MetricRecordService;
import com.illumio.data.service.ServiceKeyAuth;
import com.illumio.data.service.WidgetBatchService;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/tenant/{tenantId}/insights")
public class WidgetBatchController {
    private final WidgetBatchService widgetBatchService;
    private final MeterRegistry meterRegistry;
    private final MetricRecordService metricRecordService;
    private final InsightsServiceConfiguration config;
    private final JWTService jwtService;
    private final ServiceKeyAuth serviceKeyAuth;

    @PostMapping("/widgets/batch")
    public Mono<ResponseEntity<WidgetBatchResponse>> getBatchWidgetData(
            @PathVariable String tenantId,
            @RequestBody WidgetBatchRequest batchRequest,
            @RequestHeader MultiValueMap<String, String> headers,
            @RequestParam MultiValueMap<String, String> params) {

        Timer.Sample timer = Timer.start(meterRegistry);
        log.debug("Processing batch request for tenant ID: {}, with widget IDs: {}, pivot widgets: {}", 
                tenantId, batchRequest.getWidgetIdList(), 
                batchRequest.getPivotWidget() != null ? batchRequest.getPivotWidget().size() : 0);

        // Validate mandatory parameters
        if (tenantId == null || tenantId.trim().isEmpty()) {
            log.error("Missing required parameter: tenantID");
            return Mono.just(errorResponse(HttpStatus.BAD_REQUEST, "Bad Request", "Missing required parameter: tenantID."));
        }

        // First check if the request is from an authorized service that doesn't use census JWT mechanism. If not, proceed with JWT auth.
        if (!serviceKeyAuth.isAuthorized(headers)) {
            log.warn("Service key auth failed, proceeding with JWT auth");
        } else {
            // JWT validation
            if (config.getJwtConfig() != null && config.getJwtConfig().getEnableJwt() != null
                    && config.getJwtConfig().getEnableJwt()) {
                log.info("JWT enabled with header {}, with symmetric mode: {}", headers, config.getJwtConfig().getIsSymmetricKey());
                Pair<Boolean, String> jwtValidation = jwtService.validateJWT(headers, tenantId, config.getJwtConfig().getIsSymmetricKey());
                log.info("JWT validation result {}", jwtValidation);
                if (!jwtValidation.getLeft()) {
                    return Mono.just(
                            errorResponse(
                                    HttpStatus.UNAUTHORIZED,
                                    "Unauthorized",
                                    "Invalid JWT: " + jwtValidation.getRight()));
                }
            } else {
                log.info("JWT disabled");
            }
        }
        log.info("Request authorized");

        if (batchRequest.getWidgetIdList() == null || batchRequest.getWidgetIdList().isEmpty()) {
            log.error("Missing required parameter: widgetIdList");
            return Mono.just(errorResponse(HttpStatus.BAD_REQUEST, "Bad Request", "Widget IDs list cannot be empty."));
        }

        if (batchRequest.getCurrentTimeFrame() == null) {
            log.error("Missing required parameter: currentTimeFrame");
            return Mono.just(errorResponse(HttpStatus.BAD_REQUEST, "Bad Request", "Current time frame is required."));
        }

        if (batchRequest.getComparisonTimeFrame() == null) {
            log.error("Missing required parameter: comparisonTimeFrame");
            return Mono.just(errorResponse(HttpStatus.BAD_REQUEST, "Bad Request", "Comparison time frame is required."));
        }

        // Validate pivot widgets if present
        if (batchRequest.getPivotWidget() != null && !batchRequest.getPivotWidget().isEmpty()) {
            String pivotValidationError = validatePivotWidgets(batchRequest.getPivotWidget());
            if (pivotValidationError != null) {
                log.error("Pivot widget validation failed: {}", pivotValidationError);
                return Mono.just(errorResponse(HttpStatus.BAD_REQUEST, "Bad Request", pivotValidationError));
            }
        }

        return widgetBatchService.processBatchRequest(tenantId, batchRequest, headers, params)
                .map(ResponseEntity::ok)
                .doOnSuccess(result -> {
                    boolean hasPivotWidgets = batchRequest.getPivotWidget() != null && !batchRequest.getPivotWidget().isEmpty();
                    log.debug("Successfully processed batch request for tenant ID: {} (with pivot widgets: {})", 
                            tenantId, hasPivotWidgets);
                    metricRecordService.recordTimeAndCountMetrics(
                            meterRegistry,
                            timer,
                            Metadata.builder()
                                    .widgetId("0000")
                                    .requestType(hasPivotWidgets ? "widgetBatchPivotInsight" : "widgetBatchInsight")
                                    .pageId("batch")
                                    .build(),
                            "success",
                            "Time taken to process batch widget request",
                            tenantId
                    );
                })
                .onErrorResume(error -> {
                    log.error("Error processing batch request: ", error);
                    metricRecordService.recordTimeAndCountMetrics(
                            meterRegistry,
                            timer,
                            Metadata.builder().widgetId("0000").requestType("widgetBatchInsight").pageId("batch").build(),
                            "error",
                            "Error processing batch widget request",
                            tenantId
                    );
                    return Mono.just(errorResponse(HttpStatus.INTERNAL_SERVER_ERROR, "Internal Server Error",
                            "Batch processing failed: " + error.getMessage()));
                });
    }

    /**
     * Validate pivot widgets configuration
     */
    private String validatePivotWidgets(List<WidgetBatchRequest.PivotWidget> pivotWidgets) {
        for (WidgetBatchRequest.PivotWidget pivotWidget : pivotWidgets) {
            if (pivotWidget.getWidgetId() == null || pivotWidget.getWidgetId().trim().isEmpty()) {
                return "Pivot widget ID cannot be empty.";
            }
            
            if (pivotWidget.getPageId() == null || pivotWidget.getPageId().trim().isEmpty()) {
                return "Pivot widget page ID cannot be empty.";
            }
            
            if (pivotWidget.getField() == null || pivotWidget.getField().trim().isEmpty()) {
                return "Pivot widget field cannot be empty.";
            }
            
            if (pivotWidget.getTopKSize() != null && pivotWidget.getTopKSize() <= 0) {
                return "Pivot widget topKSize must be a positive number.";
            }
            
            if (pivotWidget.getTopKSize() != null && pivotWidget.getTopKSize() > 100) {
                return "Pivot widget topKSize cannot exceed 100.";
            }
        }
        return null; // No validation errors
    }

    private ResponseEntity<WidgetBatchResponse> errorResponse(HttpStatus status, String error, String message) {
        WidgetBatchResponse.WidgetResult errorResult = WidgetBatchResponse.WidgetResult.builder()
                .widgetId("batch")
                .status("error")
                .message(message)
                .build();

        WidgetBatchResponse response = WidgetBatchResponse.builder()
                .results(java.util.List.of(errorResult))
                .build();
        return new ResponseEntity<>(response, status);
    }
}