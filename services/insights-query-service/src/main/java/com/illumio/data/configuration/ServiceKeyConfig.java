package com.illumio.data.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;


import java.util.Map;

@Configuration
@ConfigurationProperties(prefix = "service-auth")
@Getter
@Setter
public class ServiceKeyConfig {
    // serviceName -> authKey
    private Map<String, String> keys;

    public boolean isValidKey(String serviceName, String providedKey) {
        return providedKey != null && providedKey.equals(keys.get(serviceName));
    }
}
