package com.illumio.data.service;

import com.illumio.data.configuration.ServiceKeyConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

@Component
@RequiredArgsConstructor
public class ServiceKeyAuth {

    private final ServiceKeyConfig serviceKeyConfig;

    public boolean isAuthorized(MultiValueMap<String, String> headers) {
        String serviceName = headers.getFirst("X-origin-service-name");
        String serviceKey = headers.getFirst("X-service-auth-key");
        if (serviceName == null || serviceKey == null) {
            return false;
        }

        return serviceKeyConfig.isValidKey(serviceName, serviceKey);
    }
}
