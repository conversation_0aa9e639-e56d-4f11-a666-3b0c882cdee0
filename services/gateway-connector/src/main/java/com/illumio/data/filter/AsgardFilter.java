package com.illumio.data.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriBuilder;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;

@Slf4j
@Component
public class AsgardFilter extends AbstractGatewayFilterFactory<AsgardFilter.Config> {

    public AsgardFilter() {
        super(Config.class); // <== REQUIRED
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            // Log the configured message (can be set in YAML)
            log.info("AsgardFilter message: {}", config.getMessage());

            ServerHttpRequest request = exchange.getRequest();
            String referer = request.getHeaders().getFirst("referer");

            String subdomain = extractSubdomainFromReferer(referer);

            // Optional: Add a header
            exchange.getRequest()
                    .mutate()
                    .header("X-Asgard-Filter", config.getMessage())
                    .header("X-tenant-subdomain", subdomain)
                    .header("redirectURL", referer)
                    .build();
            log.info("AsgardFilter message: {}", config.getMessage());
            log.info("AsgardFilter new headers: {}", exchange.getRequest().getHeaders());
            return chain.filter(exchange);
        };
    }

    private String extractSubdomainFromReferer(String referer) {
        try {
            if (referer == null) return null;
            URI uri = UriComponentsBuilder.fromUriString(referer).build().toUri();
            String host = uri.getHost();
            if (host == null) return null;

            String[] parts = host.split("\\.");
            if (parts.length >= 3) {
                return parts[0]; // 'xyz' from 'xyz.console.illumio.io'
            }
        } catch (Exception e) {
            log.error("Error extracting subdomain from referer: {}", referer, e);
        }
        return null;
    }

    public static class Config {
        private String message;
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
}
