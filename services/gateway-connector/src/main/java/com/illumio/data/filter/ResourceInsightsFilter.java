package com.illumio.data.filter;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;

@Slf4j
@Component
public class ResourceInsightsFilter extends AbstractGatewayFilterFactory<ResourceInsightsFilter.Config> {

    @Value("${resource-insights.tenant-mapping.sourceTenantId:}")
    private String sourceTenantId;

    @Value("${resource-insights.tenant-mapping.targetTenantId:}")
    private String targetTenantId;

    public ResourceInsightsFilter() {
        super(Config.class);
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            log.info("ResourceInsightsFilter: {}", config.getMessage());
            log.debug("Routing request to IQS: {}", exchange.getRequest().getPath());
            
            // Check for tenant ID mapping
            ServerHttpRequest request = exchange.getRequest();
            String illumioTenantId = request.getHeaders().getFirst("x-illumio-tenant-id");
            
            // TODO: This is a temporary solution to map from one tenant id to another based on the config being used for MSG
            if (illumioTenantId != null && sourceTenantId != null && !sourceTenantId.isEmpty() && targetTenantId != null && !targetTenantId.isEmpty()) {
                if (illumioTenantId.equals(sourceTenantId)) {
                    log.info("ResourceInsightsFilter: Mapping tenant ID from {} to {}", sourceTenantId, targetTenantId);
                    
                    ServerHttpRequest mutatedRequest = request.mutate()
                            .header("x-illumio-tenant-id", targetTenantId)
                            .build();
                    
                    ServerWebExchange mutatedExchange = exchange.mutate().request(mutatedRequest).build();
                    return chain.filter(mutatedExchange);
                }
            }
            
            // Pass the request through to IQS
            return chain.filter(exchange);
        };
    }

    @Getter
    @Setter
    public static class Config {
        private String message;
    }
}