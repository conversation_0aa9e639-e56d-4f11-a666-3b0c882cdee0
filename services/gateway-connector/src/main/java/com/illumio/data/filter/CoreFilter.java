package com.illumio.data.filter;

import authservice.GetUserSessionPermissionsResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.util.JsonFormat;
import com.illumio.data.config.JwtClaimsMapper;
import com.illumio.data.service.JwtService;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.OrderedGatewayFilter;
import org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static com.illumio.data.constants.Constants.PERMISSIONS_SIZE;
import static com.illumio.data.constants.Constants.GATEWAY_TOKEN;
import static com.illumio.data.constants.Constants.SCHEME;
import static com.illumio.data.constants.Constants.TIMEZONE;
import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.GATEWAY_REQUEST_URL_ATTR;

@Slf4j
@Component
public class CoreFilter extends AbstractGatewayFilterFactory<CoreFilter.Config> {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final JwtService jwtService;
    private final Counter largePermissionsCounter;

    public CoreFilter(JwtService jwtService, MeterRegistry meterRegistry) {
        super(Config.class);
        this.jwtService = jwtService;
        this.largePermissionsCounter = meterRegistry.counter("gateway.large_permissions_counter");
    }

    @Override
    public GatewayFilter apply(Config config) {
        return new OrderedGatewayFilter((exchange, chain) -> {
            GetUserSessionPermissionsResponse grpcResponse = exchange.getAttribute("grpcSession");
            String csi_token = exchange.getRequest().getHeaders().getFirst("X-csi");
            if (grpcResponse == null) {
                log.warn("gRPC session not found in request attributes");
                return chain.filter(exchange);
            }

            try {
                // === Extract target FQDN and port from gRPC response ===
                String fqdn = grpcResponse.getCore().getPceFqdn().split(":")[0];
                ServerHttpRequest request = exchange.getRequest();
                String existingPath = request.getURI().getRawPath();
                log.info("Current api path is: {}", existingPath);
                URI newUri = UriComponentsBuilder
                        .newInstance()
                        .scheme(SCHEME) // explicitly set scheme
                        .host(fqdn)
                        .path(existingPath)
                        .build(true)
                        .toUri();

                log.info("New URI with preserved path: {}", newUri);
                // === Clone headers from original request ===
                HttpHeaders newHeaders = new HttpHeaders();
                newHeaders.putAll(exchange.getRequest().getHeaders());

                String last_updated_at = grpcResponse.getPermLastUpdated();

                String csiToken = exchange.getRequest().getHeaders().getFirst("X-CSI-Token");
                if (csiToken != null) {
                    newHeaders.set("X-Csi", csiToken);
                }

                String json = JsonFormat.printer()
                        .preservingProtoFieldNames()
                        .print(grpcResponse.getCore());

                boolean permissions_excluded = false;
                Map<String, Object> jwtClaims = new HashMap<>();
                if (json.getBytes(StandardCharsets.UTF_8).length > PERMISSIONS_SIZE) {
                    largePermissionsCounter.increment();
                    log.warn("response > 4KB, skipping JWE and setting X-Permissions-Offloaded header");
                    //TODO Add metrics to check how many requests are >4kb
                    jwtClaims = new HashMap<>(JwtClaimsMapper.mapCoreToJwtClaim(grpcResponse));
                    log.info("response > 4kb {}", jwtClaims);
                    permissions_excluded = true;
                } else {
                    // Convert gRPC message to JSON and encrypt to JWE
                    jwtClaims.put("core",
                            new HashMap<>(objectMapper.readValue(json,
                                    new com.fasterxml.jackson.core.type.TypeReference<HashMap<String,Object>>() {}
                            )));
                }
                jwtClaims.put("csi_token", csi_token);
                jwtClaims.put("permissions_excluded", permissions_excluded);
                jwtClaims.put("last_updated_at", last_updated_at);


                log.info("Sending response to Core : {}", jwtClaims);

                // === Mutate request with new URI and headers ===
                String jwt = jwtService.generatePermissionsJwt(jwtClaims, grpcResponse.getCore().getSessionTimeoutMinutes());


                newHeaders.set(GATEWAY_TOKEN, jwt);

                ServerHttpRequest mutatedRequest = exchange.getRequest()
                        .mutate()
                        .uri(newUri)
                        .headers(httpHeaders -> {
                            httpHeaders.clear();
                            httpHeaders.putAll(newHeaders);
                        })
                        .build();
                mutatedRequest.getHeaders().forEach((key, value) -> {
                    log.info("Mutated Request Header: {} = {}", key, value);
                });
                exchange.getAttributes().put(GATEWAY_REQUEST_URL_ATTR, mutatedRequest.getURI());
                return chain.filter(exchange.mutate().request(mutatedRequest).build());

            } catch (Exception e) {
                log.error("CoreFilter failed", e);
                return Mono.error(e);
            }
        }, RouteToRequestUrlFilter.ROUTE_TO_URL_FILTER_ORDER+1);

    }

    @Getter
    @Setter
    public static class Config {
        private String message;
    }
}