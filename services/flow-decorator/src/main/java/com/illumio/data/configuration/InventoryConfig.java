package com.illumio.data.configuration;

import inventory.ReactorInventoryCacheServiceGrpc;

import io.grpc.Grpc;
import io.grpc.InsecureChannelCredentials;
import io.grpc.ManagedChannel;
import io.grpc.TlsChannelCredentials;
import io.grpc.netty.shaded.io.netty.handler.ssl.util.InsecureTrustManagerFactory;

import lombok.Data;
import lombok.SneakyThrows;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.ByteArrayInputStream;
import java.util.Base64;

@Configuration
@ConfigurationProperties(prefix = "inventory-config")
@Data
public class InventoryConfig {
    private String host;
    private Integer port;
    private Boolean useTls;
    private String cert;

    @Bean
    @SneakyThrows
    public ReactorInventoryCacheServiceGrpc.ReactorInventoryCacheServiceStub stub() {
        ManagedChannel managedChannel = createManagedChannel();
        return ReactorInventoryCacheServiceGrpc.newReactorStub(managedChannel);
    }

    @SneakyThrows
    private ManagedChannel createManagedChannel() {
        if (Boolean.FALSE.equals(useTls)) {
            return Grpc.newChannelBuilderForAddress(host, port, InsecureChannelCredentials.create())
                    .build();
        } else {
            var credsBuilder = TlsChannelCredentials.newBuilder();
            if (cert != null && !cert.isEmpty()) {
                credsBuilder.trustManager(new ByteArrayInputStream(Base64.getDecoder().decode(cert)));
            } else {
                // Use insecure trust manager if cert is empty
                credsBuilder.trustManager(InsecureTrustManagerFactory.INSTANCE.getTrustManagers());
            }
            return Grpc.newChannelBuilderForAddress(host, port, credsBuilder.build())
                    .build();
        }
    }
}
